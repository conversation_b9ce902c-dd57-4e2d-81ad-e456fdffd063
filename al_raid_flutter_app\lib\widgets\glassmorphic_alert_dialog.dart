import 'package:flutter/material.dart';
import 'dart:ui';
import 'glassmorphic_card.dart';
import 'gradient_button.dart';

class GlassmorphicAlertDialog extends StatelessWidget {
  final String title;
  final String message;
  final bool isSuccess;
  final String? confirmText;
  final String? cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final bool showCancelButton;
  final IconData? icon;

  const GlassmorphicAlertDialog({
    super.key,
    required this.title,
    required this.message,
    this.isSuccess = true,
    this.confirmText,
    this.cancelText,
    this.onConfirm,
    this.onCancel,
    this.showCancelButton = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          child: GlassmorphicCard(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: isSuccess
                          ? [Colors.green.shade400, Colors.green.shade600]
                          : [Colors.red.shade400, Colors.red.shade600],
                    ),
                  ),
                  child: Icon(
                    icon ?? (isSuccess ? Icons.check : Icons.error),
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(height: 20),

                // Title
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),

                // Message
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 16,
                    color: isDark ? Colors.white70 : Colors.black54,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Buttons
                Row(
                  children: [
                    if (showCancelButton) ...[
                      Expanded(
                        child: TextButton(
                          onPressed: onCancel ?? () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(
                                color: isDark ? Colors.white30 : Colors.black26,
                              ),
                            ),
                          ),
                          child: Text(
                            cancelText ?? 'Cancel',
                            style: TextStyle(
                              color: isDark ? Colors.white70 : Colors.black54,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                    ],
                    Expanded(
                      child: GradientButton(
                        text: confirmText ?? 'OK',
                        onPressed: onConfirm ?? () => Navigator.of(context).pop(),
                        height: 48,
                        gradient: isSuccess
                            ? const LinearGradient(
                                colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
                              )
                            : const LinearGradient(
                                colors: [Color(0xFFE53E3E), Color(0xFFD53F8C)],
                              ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  static Future<void> show({
    required BuildContext context,
    required String title,
    required String message,
    bool isSuccess = true,
    String? confirmText,
    String? cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool showCancelButton = false,
    IconData? icon,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (BuildContext context) {
        return GlassmorphicAlertDialog(
          title: title,
          message: message,
          isSuccess: isSuccess,
          confirmText: confirmText,
          cancelText: cancelText,
          onConfirm: onConfirm,
          onCancel: onCancel,
          showCancelButton: showCancelButton,
          icon: icon,
        );
      },
    );
  }
}
