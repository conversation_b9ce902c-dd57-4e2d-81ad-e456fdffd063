import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class AuthProvider with ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  String? _error;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;
  bool get isMerchant => _user?.role == 'merchant';
  bool get isBuyer => _user?.role == 'buyer';

  final ApiService _apiService = ApiService();

  AuthProvider() {
    _loadUserFromPrefs();
  }

  Future<void> _loadUserFromPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString('user_data');
    if (userData != null) {
      try {
        _user = User.fromJson(userData);
        notifyListeners();
      } catch (e) {
        debugPrint('Error loading user from preferences: $e');
      }
    }
  }

  Future<void> _saveUserToPrefs(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_data', user.toJson());
  }

  Future<void> _removeUserFromPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_data');
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _apiService.login(email, password);

      if (result['success'] == true) {
        _user = User.fromMap(result['user']);
        await _saveUserToPrefs(_user!);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? 'Login failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> register({
    required String fullName,
    required String email,
    required String phoneNumber,
    required String password,
    required String shippingAddress,
    required String country,
    required String role,
    String? commercialRegisterPhoto,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _apiService.register(
        fullName: fullName,
        email: email,
        phoneNumber: phoneNumber,
        password: password,
        shippingAddress: shippingAddress,
        country: country,
        role: role,
        commercialRegisterPhoto: commercialRegisterPhoto,
      );

      if (result['success'] == true) {
        _user = User.fromMap(result['user']);
        await _saveUserToPrefs(_user!);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? 'Registration failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    _user = null;
    await _removeUserFromPrefs();
    notifyListeners();
  }

  Future<bool> updateProfile({
    required String fullName,
    required String phoneNumber,
    required String shippingAddress,
    required String country,
  }) async {
    if (_user == null) return false;

    _setLoading(true);
    _setError(null);

    try {
      // Here you would call an API to update the profile
      // For now, we'll update locally
      _user = _user!.copyWith(
        fullName: fullName,
        phoneNumber: phoneNumber,
        shippingAddress: shippingAddress,
        country: country,
      );

      await _saveUserToPrefs(_user!);
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Update failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (_user == null) return false;

    _setLoading(true);
    _setError(null);

    try {
      final result = await _apiService.changePassword(
        userId: _user!.id,
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (result['success'] == true) {
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? 'Password change failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
