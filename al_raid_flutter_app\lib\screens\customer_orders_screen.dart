import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import '../providers/auth_provider.dart';
import '../providers/order_provider.dart';
import '../providers/language_provider.dart';
import '../widgets/glassmorphic_card.dart';
import '../widgets/animated_input_field.dart';
import '../models/order.dart';

class CustomerOrdersScreen extends StatefulWidget {
  const CustomerOrdersScreen({super.key});

  @override
  State<CustomerOrdersScreen> createState() => _CustomerOrdersScreenState();
}

class _CustomerOrdersScreenState extends State<CustomerOrdersScreen> {
  bool _isLoading = true;
  String _searchQuery = '';
  DateTime? _selectedDate;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCustomerOrders();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomerOrders() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);

    if (authProvider.user != null && authProvider.user!.role == 'merchant') {
      await orderProvider.loadApprovedMerchantOrders(authProvider.user!.id);
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Order> _getFilteredOrders(List<Order> orders) {
    List<Order> filtered = orders.where((order) => order.isApproved).toList();

    // Filter by search query (product name or buyer name)
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((order) {
        final productName = order.productDescription?.toLowerCase() ?? '';
        final buyerName = order.buyerName?.toLowerCase() ?? '';
        final query = _searchQuery.toLowerCase();
        return productName.contains(query) || buyerName.contains(query);
      }).toList();
    }

    // Filter by date
    if (_selectedDate != null) {
      filtered = filtered.where((order) {
        final orderDate = order.createdAt;
        return orderDate.year == _selectedDate!.year &&
            orderDate.month == _selectedDate!.month &&
            orderDate.day == _selectedDate!.day;
      }).toList();
    }

    return filtered;
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _clearDateFilter() {
    setState(() {
      _selectedDate = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isRTL = languageProvider.isArabic;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withValues(alpha: 0.1),
              theme.colorScheme.secondary.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(theme, isRTL),

              // Search and Filters
              _buildSearchAndFilters(theme, isRTL),

              // Content
              Expanded(
                child: _isLoading
                    ? _buildLoadingState(theme)
                    : _buildOrdersList(theme, isRTL),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isRTL) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              isRTL ? Icons.arrow_forward_ios : Icons.arrow_back_ios,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              isRTL ? 'طلبات العملاء' : 'Customer Orders',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0);
  }

  Widget _buildSearchAndFilters(ThemeData theme, bool isRTL) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          // Search Bar
          GlassmorphicCard(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: SearchInputField(
              hintText: isRTL ? 'البحث في الطلبات...' : 'Search orders...',
              controller: _searchController,
              onChanged: _onSearchChanged,
              onClear: () {
                _searchController.clear();
                _onSearchChanged('');
              },
            ),
          ),

          const SizedBox(height: 12),

          // Date Filter
          Row(
            children: [
              Expanded(
                child: GlassmorphicCard(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: InkWell(
                    onTap: _selectDate,
                    child: Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 20,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _selectedDate != null
                                ? DateFormat('yyyy-MM-dd')
                                    .format(_selectedDate!)
                                : (isRTL ? 'تصفية بالتاريخ' : 'Filter by date'),
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: _selectedDate != null
                                  ? theme.colorScheme.onSurface
                                  : theme.colorScheme.onSurface
                                      .withValues(alpha: 0.6),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              if (_selectedDate != null) ...[
                const SizedBox(width: 8),
                GlassmorphicCard(
                  padding: const EdgeInsets.all(8),
                  child: InkWell(
                    onTap: _clearDateFilter,
                    child: Icon(
                      Icons.clear,
                      size: 20,
                      color: theme.colorScheme.error,
                    ),
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 16),
        ],
      ),
    )
        .animate()
        .fadeIn(duration: 600.ms, delay: 200.ms)
        .slideY(begin: -0.2, end: 0);
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading customer orders...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList(ThemeData theme, bool isRTL) {
    return Consumer<OrderProvider>(
      builder: (context, orderProvider, child) {
        if (orderProvider.error != null) {
          return _buildErrorState(theme, orderProvider.error!, isRTL);
        }

        final filteredOrders = _getFilteredOrders(orderProvider.orders);

        if (filteredOrders.isEmpty) {
          return _buildEmptyState(theme, isRTL);
        }

        return RefreshIndicator(
          onRefresh: _loadCustomerOrders,
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            itemCount: filteredOrders.length,
            itemBuilder: (context, index) {
              final order = filteredOrders[index];
              return _buildOrderCard(order, index, theme, isRTL);
            },
          ),
        );
      },
    );
  }

  Widget _buildErrorState(ThemeData theme, String error, bool isRTL) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: theme.colorScheme.error.withValues(alpha: 0.6),
          ),
          const SizedBox(height: 16),
          Text(
            isRTL ? 'حدث خطأ' : 'Error occurred',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.error,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadCustomerOrders,
            child: Text(isRTL ? 'إعادة المحاولة' : 'Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme, bool isRTL) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 80,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            isRTL ? 'لا توجد طلبات معتمدة' : 'No approved orders found',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isRTL
                ? 'ستظهر الطلبات المعتمدة من قبل الإدارة هنا'
                : 'Admin-approved orders will appear here',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(Order order, int index, ThemeData theme, bool isRTL) {
    return GlassmorphicCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with Order ID and Status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  isRTL ? 'طلب #${order.id}' : 'Order #${order.id}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                _buildStatusBadge(order.status, theme),
              ],
            ),

            const SizedBox(height: 12),

            // Buyer Information
            Row(
              children: [
                Icon(
                  Icons.person,
                  size: 18,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    isRTL
                        ? 'المشتري: ${order.buyerName ?? 'غير محدد'}'
                        : 'Buyer: ${order.buyerName ?? 'Unknown'}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Product Information
            Row(
              children: [
                Icon(
                  Icons.inventory_2,
                  size: 18,
                  color: theme.colorScheme.secondary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    isRTL
                        ? 'المنتج: ${order.productDescription ?? 'غير محدد'}'
                        : 'Product: ${order.productDescription ?? 'Unknown'}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Price Information
            Row(
              children: [
                Icon(
                  Icons.attach_money,
                  size: 18,
                  color: theme.colorScheme.tertiary,
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL
                      ? 'السعر: \$${order.price?.toStringAsFixed(2) ?? '0.00'}'
                      : 'Price: \$${order.price?.toStringAsFixed(2) ?? '0.00'}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.tertiary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Order Date
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 18,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL
                      ? 'تاريخ الطلب: ${DateFormat('yyyy-MM-dd').format(order.createdAt)}'
                      : 'Order Date: ${DateFormat('yyyy-MM-dd').format(order.createdAt)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),

            // Contact Information (if available)
            if (order.buyerEmail != null || order.buyerPhone != null) ...[
              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),
              if (order.buyerEmail != null)
                Row(
                  children: [
                    Icon(
                      Icons.email,
                      size: 16,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        order.buyerEmail!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.6),
                        ),
                      ),
                    ),
                  ],
                ),
              if (order.buyerPhone != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.phone,
                      size: 16,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      order.buyerPhone!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ],
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideX(begin: 0.3, end: 0);
  }

  Widget _buildStatusBadge(String status, ThemeData theme) {
    Color backgroundColor;
    Color textColor;
    String displayText;

    switch (status.toLowerCase()) {
      case 'approved':
        backgroundColor = theme.colorScheme.tertiary;
        textColor = Colors.white;
        displayText = 'Approved';
        break;
      case 'under_review':
        backgroundColor = theme.colorScheme.secondary;
        textColor = Colors.white;
        displayText = 'Under Review';
        break;
      case 'rejected':
        backgroundColor = theme.colorScheme.error;
        textColor = Colors.white;
        displayText = 'Rejected';
        break;
      default:
        backgroundColor = theme.colorScheme.outline;
        textColor = theme.colorScheme.onSurface;
        displayText = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        displayText,
        style: theme.textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }
}
