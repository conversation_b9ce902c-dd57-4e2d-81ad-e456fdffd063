// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'AL Raid';

  @override
  String get appSubtitle => 'Industrial and Food Products Platform';

  @override
  String get welcome => 'Welcome';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get firstName => 'First Name';

  @override
  String get lastName => 'Last Name';

  @override
  String get phone => 'Phone Number';

  @override
  String get userType => 'User Type';

  @override
  String get buyer => 'Buyer';

  @override
  String get merchant => 'Merchant';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get termsText =>
      'By using this application, you agree to our terms and conditions. Please read them carefully before proceeding.';

  @override
  String get acceptTerms => 'I accept the terms and conditions';

  @override
  String get continueButton => 'Continue';

  @override
  String get home => 'Home';

  @override
  String get products => 'Products';

  @override
  String get orders => 'Orders';

  @override
  String get profile => 'Profile';

  @override
  String get search => 'Search';

  @override
  String get searchProducts => 'Search products...';

  @override
  String get categories => 'Categories';

  @override
  String get allCategories => 'All Categories';

  @override
  String get industrial => 'Industrial';

  @override
  String get food => 'Food';

  @override
  String get electronics => 'Electronics';

  @override
  String get automotive => 'Automotive';

  @override
  String get textiles => 'Textiles';

  @override
  String get chemicals => 'Chemicals';

  @override
  String get machinery => 'Machinery';

  @override
  String get agriculture => 'Agriculture';

  @override
  String get featuredProducts => 'Featured Products';

  @override
  String get viewAll => 'View All';

  @override
  String get productDetails => 'Product Details';

  @override
  String get specifications => 'Specifications';

  @override
  String get description => 'Description';

  @override
  String get price => 'Price';

  @override
  String get quantity => 'Quantity';

  @override
  String get addToCart => 'Add to Cart';

  @override
  String get buyNow => 'Buy Now';

  @override
  String get addProduct => 'Add Product';

  @override
  String get productName => 'Product Name';

  @override
  String get productDescription => 'Product Description';

  @override
  String get productPrice => 'Product Price';

  @override
  String get productCategory => 'Product Category';

  @override
  String get productImages => 'Product Images';

  @override
  String get addImages => 'Add Images';

  @override
  String get selectCategory => 'Select Category';

  @override
  String get saveProduct => 'Save Product';

  @override
  String get myProfile => 'My Profile';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get changePassword => 'Change Password';

  @override
  String get language => 'Language';

  @override
  String get theme => 'Theme';

  @override
  String get notifications => 'Notifications';

  @override
  String get logout => 'Logout';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get confirm => 'Confirm';

  @override
  String get success => 'Success';

  @override
  String get error => 'Error';

  @override
  String get loading => 'Loading...';

  @override
  String get retry => 'Retry';

  @override
  String get noProductsFound => 'No products found';

  @override
  String get noOrdersFound => 'No orders found';

  @override
  String get orderHistory => 'Order History';

  @override
  String get orderDetails => 'Order Details';

  @override
  String get orderStatus => 'Order Status';

  @override
  String get pending => 'Pending';

  @override
  String get processing => 'Processing';

  @override
  String get shipped => 'Shipped';

  @override
  String get delivered => 'Delivered';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get orderDate => 'Order Date';

  @override
  String get orderTotal => 'Order Total';

  @override
  String get contactSeller => 'Contact Seller';

  @override
  String get trackOrder => 'Track Order';

  @override
  String get myProducts => 'My Products';

  @override
  String get manageOrders => 'Manage Orders';

  @override
  String get addNewProduct => 'Add New Product';

  @override
  String get editProduct => 'Edit Product';

  @override
  String get deleteProduct => 'Delete Product';

  @override
  String get productAdded => 'Product added successfully';

  @override
  String get productUpdated => 'Product updated successfully';

  @override
  String get productDeleted => 'Product deleted successfully';

  @override
  String get orderPlaced => 'Order placed successfully';

  @override
  String get profileUpdated => 'Profile updated successfully';

  @override
  String get passwordChanged => 'Password changed successfully';

  @override
  String get loggedOut => 'Logged out successfully';

  @override
  String get invalidCredentials => 'Invalid email or password';

  @override
  String get registrationFailed => 'Registration failed';

  @override
  String get networkError => 'Network error. Please try again.';

  @override
  String get requiredField => 'This field is required';

  @override
  String get invalidEmail => 'Please enter a valid email';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get passwordsNotMatch => 'Passwords do not match';

  @override
  String get invalidPhone => 'Please enter a valid phone number';

  @override
  String get sar => 'SAR';

  @override
  String get currency => 'SAR';

  @override
  String get dark => 'Dark';

  @override
  String get light => 'Light';

  @override
  String get system => 'System';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'English';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get selectTheme => 'Select Theme';

  @override
  String get all => 'All';

  @override
  String get approved => 'Approved';

  @override
  String get rejected => 'Rejected';

  @override
  String get filterByStatus => 'Filter by Status';

  @override
  String get noProductsForStatus => 'No products found for this status';

  @override
  String get productType => 'Product Type';
}
